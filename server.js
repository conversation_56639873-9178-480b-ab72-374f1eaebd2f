// Simple Express server for bansheeblast Library API
const express = require('express');
const app = express();
const cors = require('cors');  // Import the cors middleware
const PORT = process.env.PORT || 3001;

app.use(cors());  // Enable CORS for all routes
app.use(express.static('.')); // Serve static files from current directory

app.use(express.json());

// Sample track data for demo - Enhanced with more realistic data
const sampleTracks = {
  101: { id: 101, title: "Midnight Dreams", artist: "Luna Echo", album: "Neon Nights", duration: "3:45", cover: "imgs/album-01.png", genre: "Electronic", year: 2023, plays: 1250 },
  102: { id: 102, title: "Ocean Waves", artist: "Cosmic Drift", album: "Deep Space", duration: "4:12", cover: "imgs/album-02.png", genre: "Ambient", year: 2023, plays: 890 },
  103: { id: 103, title: "City Lights", artist: "Neon Pulse", album: "Urban Vibes", duration: "3:28", cover: "imgs/album-03-B.png", genre: "Synthwave", year: 2024, plays: 2100 },
  104: { id: 104, title: "Starfall", artist: "Galaxy Dreams", album: "Celestial", duration: "5:03", cover: "imgs/album-04-B.png", genre: "Chillout", year: 2023, plays: 1680 },
  105: { id: 105, title: "Electric Soul", artist: "Synth Wave", album: "Digital Hearts", duration: "4:21", cover: "imgs/album-01.png", genre: "Electronic", year: 2024, plays: 950 },
  106: { id: 106, title: "Neon Rain", artist: "Cyber City", album: "Future Noir", duration: "3:56", cover: "imgs/album-02.png", genre: "Cyberpunk", year: 2024, plays: 1420 },
  107: { id: 107, title: "Sunset Boulevard", artist: "Retro Wave", album: "80s Revival", duration: "4:35", cover: "imgs/album-03-B.png", genre: "Synthwave", year: 2023, plays: 1800 },
  108: { id: 108, title: "Digital Dreams", artist: "Pixel Heart", album: "Virtual Reality", duration: "3:15", cover: "imgs/album-04-B.png", genre: "Chiptune", year: 2024, plays: 720 },
  109: { id: 109, title: "Cosmic Journey", artist: "Space Odyssey", album: "Interstellar", duration: "6:22", cover: "imgs/album-01.png", genre: "Ambient", year: 2023, plays: 1100 },
  110: { id: 110, title: "Neon Nights", artist: "City Runner", album: "Urban Escape", duration: "3:48", cover: "imgs/album-02.png", genre: "Electronic", year: 2024, plays: 1350 }
};

// Example in-memory data (replace with DB later) - Enhanced with more realistic data
let userLibrary = {
  playlists: [
    {
      id: 1,
      name: 'Favorites',
      tracks: [101, 102, 104, 107],
      description: 'My all-time favorite tracks',
      cover: 'imgs/playlist-01.png',
      created: '2024-01-15',
      lastModified: '2024-06-20'
    },
    {
      id: 2,
      name: 'Chill Vibes',
      tracks: [103, 105, 109],
      description: 'Perfect for relaxing and unwinding',
      cover: 'imgs/playlist-02.png',
      created: '2024-02-10',
      lastModified: '2024-06-18'
    },
    {
      id: 3,
      name: 'Workout Mix',
      tracks: [106, 101, 110, 103],
      description: 'High energy tracks for the gym',
      cover: 'imgs/playlist-03.png',
      created: '2024-03-05',
      lastModified: '2024-06-25'
    },
    {
      id: 4,
      name: 'Night Drive',
      tracks: [107, 106, 104],
      description: 'Perfect soundtrack for late night drives',
      cover: 'imgs/playlist-04.png',
      created: '2024-04-12',
      lastModified: '2024-06-22'
    },
    {
      id: 5,
      name: 'Study Focus',
      tracks: [102, 109, 104],
      description: 'Ambient tracks for concentration',
      cover: 'imgs/playlist-05.png',
      created: '2024-05-08',
      lastModified: '2024-06-15'
    }
  ],
  likedSongs: [101, 104, 105, 107, 109],
  recentlyPlayed: [103, 110, 101, 106, 108],
  topArtists: ['Luna Echo', 'Cosmic Drift', 'Neon Pulse', 'Galaxy Dreams'],
  stats: {
    totalPlaylists: 5,
    totalLikedSongs: 5,
    totalListeningTime: '24h 32m',
    topGenre: 'Electronic'
  }
};

// Get all playlists
app.get('/api/library', (req, res) => {
    res.json(userLibrary);
  });


// Get track details
app.get('/api/tracks/:id', (req, res) => {
  const trackId = parseInt(req.params.id);
  const track = sampleTracks[trackId];
  if (track) {
    res.json(track);
  } else {
    res.status(404).json({ error: 'Track not found' });
  }
});

// Get all playlists
app.get('/api/library/playlists', (req, res) => {
  res.json(userLibrary.playlists);
});

// Get liked songs with track details
app.get('/api/library/liked', (req, res) => {
  const likedTracksWithDetails = userLibrary.likedSongs.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(likedTracksWithDetails);
});

// Get recently played tracks
app.get('/api/library/recent', (req, res) => {
  const recentTracksWithDetails = userLibrary.recentlyPlayed.map(trackId => sampleTracks[trackId]).filter(Boolean);
  res.json(recentTracksWithDetails);
});

// Get user stats
app.get('/api/library/stats', (req, res) => {
  res.json(userLibrary.stats);
});

// Get top artists
app.get('/api/library/artists', (req, res) => {
  res.json(userLibrary.topArtists);
});

// Search library
app.get('/api/library/search', (req, res) => {
  const query = req.query.q?.toLowerCase() || '';
  if (!query) {
    return res.json({ tracks: [], playlists: [] });
  }

  // Search tracks
  const matchingTracks = Object.values(sampleTracks).filter(track =>
    track.title.toLowerCase().includes(query) ||
    track.artist.toLowerCase().includes(query) ||
    track.album.toLowerCase().includes(query) ||
    track.genre.toLowerCase().includes(query)
  );

  // Search playlists
  const matchingPlaylists = userLibrary.playlists.filter(playlist =>
    playlist.name.toLowerCase().includes(query) ||
    playlist.description.toLowerCase().includes(query)
  );

  res.json({
    tracks: matchingTracks,
    playlists: matchingPlaylists
  });
});

// Add a new playlist
app.post('/api/library/playlists', (req, res) => {
  const { name, description = '' } = req.body;
  const newPlaylist = {
    id: Date.now(),
    name,
    tracks: [],
    description,
    cover: 'imgs/playlist-default.png',
    created: new Date().toISOString().split('T')[0],
    lastModified: new Date().toISOString().split('T')[0]
  };
  userLibrary.playlists.push(newPlaylist);
  userLibrary.stats.totalPlaylists = userLibrary.playlists.length;
  res.status(201).json(newPlaylist);
});

// Delete a playlist
app.delete('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const index = userLibrary.playlists.findIndex(p => p.id === id);
  if (index !== -1) {
    userLibrary.playlists.splice(index, 1);
    res.json({ success: true });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add/Remove a song to liked songs
app.post('/api/library/liked/:songId', (req, res) => {
    const songId = parseInt(req.params.songId);
    if (!userLibrary.likedSongs.includes(songId)) {
      userLibrary.likedSongs.push(songId);
      res.status(201).json({ success: true, liked: true, songId });
    } else {
        removeLikedSong(songId, res);
    }
});

// Remove a song from liked songs
app.delete('/api/library/liked/:songId', (req, res) => {
  const songId = parseInt(req.params.songId);
  removeLikedSong(songId, res);
});

function removeLikedSong(songId, res) {
    const idx = userLibrary.likedSongs.indexOf(songId);
    if (idx !== -1) {
      userLibrary.likedSongs.splice(idx, 1);
      res.json({ success: true, liked: false, songId });
    } else {
      res.status(404).json({ error: 'Song not found in liked songs' });
    }
}

// Edit a playlist (name, description, etc.)
app.put('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const { name, description, cover } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    if (name) playlist.name = name;
    if (description !== undefined) playlist.description = description;
    if (cover) playlist.cover = cover;
    playlist.lastModified = new Date().toISOString().split('T')[0];
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Get a single playlist by ID
app.get('/api/library/playlists/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    res.json(playlist);
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

// Add a track to a playlist
app.post('/api/library/playlists/:id/tracks', (req, res) => {
  const id = parseInt(req.params.id);
  const { trackId } = req.body;
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist && trackId) {
    if (!playlist.tracks.includes(trackId)) {
      playlist.tracks.push(trackId);
    }
    res.json({ success: true, playlist });
  } else {
    res.status(404).json({ error: 'Playlist not found or invalid trackId' });
  }
});

// Remove a track from a playlist
app.delete('/api/library/playlists/:id/tracks/:trackId', (req, res) => {
  const id = parseInt(req.params.id);
  const trackId = parseInt(req.params.trackId);
  const playlist = userLibrary.playlists.find(p => p.id === id);
  if (playlist) {
    const idx = playlist.tracks.indexOf(trackId);
    if (idx !== -1) {
      playlist.tracks.splice(idx, 1);
      res.json({ success: true, playlist });
    } else {
      res.status(404).json({ error: 'Track not found in playlist' });
    }
  } else {
    res.status(404).json({ error: 'Playlist not found' });
  }
});

app.listen(PORT, () => {
  console.log(`Library API server running on port ${PORT}`);
});
