// Enhanced Home Page Management System
class HomePageManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.initializeAnimations();
    }

    bindElements() {
        // Hero elements
        this.heroStats = document.querySelectorAll('.hero-stats .stat-number');
        this.ctaButtons = document.querySelectorAll('.cta-button');
        this.quickLinks = document.querySelectorAll('.quick-link.enhanced');
        
        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // CTA button interactions
        this.ctaButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                this.handleCTAClick(e, button);
            });
        });

        // Quick link interactions
        this.quickLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                this.handleQuickLinkClick(e, link);
            });

            link.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.handleQuickLinkClick(e, link);
                }
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'h') {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
                this.announceAction('Scrolled to top');
            }
        });
    }

    initializeAnimations() {
        // Animate hero stats on page load
        this.animateHeroStats();
        
        // Add intersection observer for sections
        this.observeSections();
        
        // Add parallax effect to stars
        this.initializeParallax();
    }

    animateHeroStats() {
        this.heroStats.forEach((stat, index) => {
            const finalValue = stat.textContent;
            const numericValue = parseFloat(finalValue.replace(/[^\d.]/g, ''));
            const suffix = finalValue.replace(/[\d.]/g, '');
            
            if (!isNaN(numericValue)) {
                // Animate from 0 to final value
                let currentValue = 0;
                const increment = numericValue / 60;
                const duration = 2000; // 2 seconds
                const stepTime = duration / 60;
                
                setTimeout(() => {
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= numericValue) {
                            currentValue = numericValue;
                            clearInterval(timer);
                        }
                        
                        stat.textContent = Math.floor(currentValue) + suffix;
                    }, stepTime);
                }, index * 300); // Stagger animations
            }
        });
    }

    observeSections() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe sections for animation
        const sections = document.querySelectorAll('.section, .quick-access');
        sections.forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    }

    initializeParallax() {
        const stars = document.querySelectorAll('.star');
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            stars.forEach((star, index) => {
                const speed = 0.1 + (index % 3) * 0.05;
                star.style.transform = `translateY(${rate * speed}px)`;
            });
        });
    }

    handleCTAClick(event, button) {
        const href = button.getAttribute('href');
        const text = button.textContent.trim();
        
        this.announceAction(`Navigating to ${text}`);
        
        // Add click animation
        button.style.transform = 'scale(0.95)';
        setTimeout(() => {
            button.style.transform = '';
        }, 150);
    }

    handleQuickLinkClick(event, link) {
        const href = link.getAttribute('href');
        const title = link.querySelector('.quick-link-title')?.textContent || 'Unknown';
        
        this.announceAction(`Opening ${title}`);
        
        // Add click animation
        link.style.transform = 'scale(0.98)';
        setTimeout(() => {
            link.style.transform = '';
        }, 150);
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;
            
            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }

    // Method to update hero statistics
    updateHeroStats(stats) {
        const elements = {
            songs: this.heroStats[0],
            artists: this.heroStats[1],
            albums: this.heroStats[2]
        };

        Object.keys(stats).forEach(key => {
            if (elements[key]) {
                elements[key].textContent = stats[key];
            }
        });
    }

    // Method to add loading shimmer effect
    addLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.add('loading-shimmer');
        });
    }

    removeLoadingShimmer(elements) {
        elements.forEach(element => {
            element.classList.remove('loading-shimmer');
        });
    }

    // Method to handle responsive behavior
    handleResize() {
        // Recalculate animations on resize
        this.initializeParallax();
    }
}

// Enhanced Star Animation System
class StarAnimationManager {
    constructor() {
        this.stars = document.querySelectorAll('.star');
        this.init();
    }

    init() {
        this.enhanceStars();
        this.addInteractivity();
    }

    enhanceStars() {
        this.stars.forEach((star, index) => {
            // Add random colors
            const colors = ['var(--neon-blue)', 'var(--cosmic-pink)', 'var(--electric-violet)', 'var(--cyber-lime)'];
            const color = colors[index % colors.length];
            
            star.style.setProperty('--star-color', color);
            star.style.background = color;
            
            // Add random sizes
            const size = 1 + Math.random() * 2;
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            
            // Add random animation delays
            const delay = Math.random() * 5;
            star.style.animationDelay = `${delay}s`;
        });
    }

    addInteractivity() {
        // Add mouse interaction
        document.addEventListener('mousemove', (e) => {
            const mouseX = e.clientX / window.innerWidth;
            const mouseY = e.clientY / window.innerHeight;
            
            this.stars.forEach((star, index) => {
                const speed = 0.02 + (index % 3) * 0.01;
                const x = (mouseX - 0.5) * speed * 50;
                const y = (mouseY - 0.5) * speed * 50;
                
                star.style.transform = `translate(${x}px, ${y}px)`;
            });
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize home page manager
    window.homeManager = new HomePageManager();
    
    // Initialize star animation manager
    window.starManager = new StarAnimationManager();
    
    // Handle window resize
    window.addEventListener('resize', () => {
        window.homeManager.handleResize();
    });
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { HomePageManager, StarAnimationManager };
}
