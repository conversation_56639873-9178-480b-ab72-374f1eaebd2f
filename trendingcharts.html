<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Trending Charts - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/trendingcharts.css">
</head>

<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main id="main-content" class="container">
        <div class="trending-charts-page">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="charts-title-gradient">Trending Charts</h1>
                <p class="page-description">Discover what's hot right now - the most popular songs, albums, and artists</p>
            </div>

            <!-- Chart Navigation -->
            <div class="chart-navigation">
                <div class="chart-tabs">
                    <button type="button" class="chart-tab active" data-chart="global">
                        <i class="fas fa-globe"></i>
                        Global
                    </button>
                    <button type="button" class="chart-tab" data-chart="country">
                        <i class="fas fa-flag"></i>
                        Your Country
                    </button>
                    <button type="button" class="chart-tab" data-chart="viral">
                        <i class="fas fa-fire"></i>
                        Viral
                    </button>
                    <button type="button" class="chart-tab" data-chart="new">
                        <i class="fas fa-star"></i>
                        New Releases
                    </button>
                </div>

                <div class="chart-filters">
                    <select id="timeRangeFilter" class="chart-filter" aria-label="Select time range for charts">
                        <option value="daily">Today</option>
                        <option value="weekly" selected>This Week</option>
                        <option value="monthly">This Month</option>
                        <option value="yearly">This Year</option>
                    </select>

                    <select id="genreFilter" class="chart-filter" aria-label="Filter charts by genre">
                        <option value="all">All Genres</option>
                        <option value="pop">Pop</option>
                        <option value="rock">Rock</option>
                        <option value="hip-hop">Hip Hop</option>
                        <option value="electronic">Electronic</option>
                        <option value="r&b">R&B</option>
                        <option value="country">Country</option>
                        <option value="jazz">Jazz</option>
                        <option value="classical">Classical</option>
                    </select>
                </div>
            </div>

            <!-- Chart Content -->
            <div class="chart-content">
                <!-- Featured Chart Hero -->
                <section class="featured-chart-hero" id="featuredChartHero">
                    <div class="hero-background">
                        <div class="hero-overlay"></div>
                        <img src="imgs/album-01.png" alt="Featured track" class="hero-bg-image" id="heroBgImage">
                    </div>
                    <div class="hero-content">
                        <div class="hero-badge">
                            <i class="fas fa-crown"></i>
                            #1 Trending
                        </div>
                        <h2 class="hero-title" id="heroTitle">Cosmic Dreams</h2>
                        <p class="hero-artist" id="heroArtist">by Stellar Waves</p>
                        <div class="hero-stats">
                            <span class="stat">
                                <i class="fas fa-play"></i>
                                <span id="heroPlays">2.4M plays</span>
                            </span>
                            <span class="stat">
                                <i class="fas fa-heart"></i>
                                <span id="heroLikes">156K likes</span>
                            </span>
                            <span class="stat">
                                <i class="fas fa-share"></i>
                                <span id="heroShares">23K shares</span>
                            </span>
                        </div>
                        <div class="hero-actions">
                            <button type="button" class="hero-play-btn" id="heroPlayBtn">
                                <i class="fas fa-play"></i>
                                Play Now
                            </button>
                            <button type="button" class="hero-action-btn" id="heroLikeBtn">
                                <i class="fas fa-heart"></i>
                                Like
                            </button>
                            <button type="button" class="hero-action-btn" id="heroAddBtn">
                                <i class="fas fa-plus"></i>
                                Add to Playlist
                            </button>
                        </div>
                    </div>
                </section>

                <!-- Charts Grid -->
                <div class="charts-grid">
                    <!-- Top Songs Chart -->
                    <section class="chart-section songs-chart" id="songsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-music"></i>
                                Top Songs
                            </h2>
                            <button type="button" class="view-all-btn" data-type="songs">
                                View All 100
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-list" id="songsChartList">
                            <!-- Songs will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Top Artists Chart -->
                    <section class="chart-section artists-chart" id="artistsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-microphone"></i>
                                Top Artists
                            </h2>
                            <button type="button" class="view-all-btn" data-type="artists">
                                View All 50
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-grid artists-grid" id="artistsChartGrid">
                            <!-- Artists will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Top Albums Chart -->
                    <section class="chart-section albums-chart" id="albumsChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-compact-disc"></i>
                                Top Albums
                            </h2>
                            <button type="button" class="view-all-btn" data-type="albums">
                                View All 50
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-grid albums-grid" id="albumsChartGrid">
                            <!-- Albums will be dynamically inserted here -->
                        </div>
                    </section>

                    <!-- Rising Stars -->
                    <section class="chart-section rising-chart" id="risingChart">
                        <div class="chart-header">
                            <h2>
                                <i class="fas fa-rocket"></i>
                                Rising Stars
                            </h2>
                            <button type="button" class="view-all-btn" data-type="rising">
                                View All
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                        <div class="chart-list rising-list" id="risingChartList">
                            <!-- Rising tracks will be dynamically inserted here -->
                        </div>
                    </section>
                </div>

                <!-- Chart Statistics -->
                <section class="chart-stats-section">
                    <h2>Chart Statistics</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalPlays">847M</div>
                                <div class="stat-label">Total Plays This Week</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-music"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalTracks">12.4K</div>
                                <div class="stat-label">Tracks in Charts</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalArtists">3.2K</div>
                                <div class="stat-label">Featured Artists</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-globe"></i>
                            </div>
                            <div class="stat-info">
                                <div class="stat-number" id="totalCountries">195</div>
                                <div class="stat-label">Countries Tracked</div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Loading State -->
            <div class="loading-state hidden" id="loadingState">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p>Loading trending charts...</p>
            </div>
        </div>

        <div id="aria-live-region" aria-live="polite" class="sr-only"></div>
    </main>

    <!-- Mini Player: Shared across pages -->
    <div class="mini-player hidden" id="miniPlayer">
        <div class="mini-player-info">
            <img src="imgs/album-01.png" alt="Current Track" class="mini-player-artwork" id="miniPlayerArtwork">
            <div class="mini-player-text">
                <h4 id="miniPlayerTitle">Track Title</h4>
                <p id="miniPlayerArtist">Artist Name</p>
            </div>
        </div>
        <div class="mini-player-controls">
            <button type="button" class="mini-control-btn" id="miniPrevBtn" aria-label="Previous track">
                <i class="fas fa-step-backward"></i>
            </button>
            <button type="button" class="mini-control-btn play-pause-btn" id="miniPlayPauseBtn" aria-label="Play/Pause">
                <i class="fas fa-play"></i>
            </button>
            <button type="button" class="mini-control-btn" id="miniNextBtn" aria-label="Next track">
                <i class="fas fa-step-forward"></i>
            </button>
        </div>
        <div class="mini-player-progress">
            <div class="progress-bar" id="miniProgressBar">
                <div class="progress-fill" id="miniProgressFill"></div>
            </div>
            <div class="time-display">
                <span id="miniCurrentTime">0:00</span>
                <span id="miniDuration">0:00</span>
            </div>
        </div>
        <div class="mini-player-actions">
            <button type="button" class="mini-control-btn" id="miniVolumeBtn" aria-label="Volume">
                <i class="fas fa-volume-up"></i>
            </button>
            <button type="button" class="mini-control-btn" id="miniExpandBtn" aria-label="Expand player">
                <i class="fas fa-expand"></i>
            </button>
            <button type="button" class="mini-control-btn" id="miniCloseBtn" aria-label="Close player">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Simple Mini Audio Player for Trendingcharts -->
    <div id="miniPlayerTest">
      <div class="mini-row">
        <button id="prevBtn" title="Previous"><span>&#9664;</span></button>
        <button id="playPauseBtn" title="Play/Pause"><span id="playPauseIcon">&#9654;</span></button>
        <button id="nextBtn" title="Next"><span>&#9654;&#9654;</span></button>
        <span id="audioStatus">Idle</span>
      </div>
      <div class="mini-info">
        <strong id="trackTitle">No Track</strong><br>
        <span id="trackArtist"></span>
      </div>
      <div class="mini-progress-row">
        <span id="currentTime">0:00</span>
        <label for="progressBar" class="sr-only">Seek</label>
        <input type="range" id="progressBar" min="0" max="100" value="0" step="1" title="Seek">
        <span id="duration">0:00</span>
      </div>
      <audio id="audioTest" preload="none"></audio>
    </div>
    <script>
      // Simple audio player logic for Trendingcharts
      let currentTrackIndex = -1;
      let currentTracks = [];
      const audio = document.getElementById('audioTest');
      const status = document.getElementById('audioStatus');
      const playPauseBtn = document.getElementById('playPauseBtn');
      const playPauseIcon = document.getElementById('playPauseIcon');
      const prevBtn = document.getElementById('prevBtn');
      const nextBtn = document.getElementById('nextBtn');
      const trackTitle = document.getElementById('trackTitle');
      const trackArtist = document.getElementById('trackArtist');
      const progressBar = document.getElementById('progressBar');
      const currentTimeSpan = document.getElementById('currentTime');
      const durationSpan = document.getElementById('duration');

      function formatTime(sec) {
        if (isNaN(sec)) return '0:00';
        const m = Math.floor(sec / 60);
        const s = Math.floor(sec % 60);
        return m + ':' + (s < 10 ? '0' : '') + s;
      }

      function updatePlayerUI() {
        if (currentTrackIndex >= 0 && currentTracks[currentTrackIndex]) {
          const track = currentTracks[currentTrackIndex];
          trackTitle.textContent = track.title || 'Unknown';
          trackArtist.textContent = track.artist || '';
          trackTitle.classList.add('playing');
        } else {
          trackTitle.textContent = 'No Track';
          trackArtist.textContent = '';
          trackTitle.classList.remove('playing');
        }
        playPauseIcon.textContent = audio.paused ? '\u25B6' : '\u23F8';
        progressBar.max = audio.duration || 0;
        progressBar.value = audio.currentTime || 0;
        currentTimeSpan.textContent = formatTime(audio.currentTime);
        durationSpan.textContent = formatTime(audio.duration);
      }

      progressBar.addEventListener('input', () => {
        audio.currentTime = progressBar.value;
      });
      audio.addEventListener('timeupdate', updatePlayerUI);
      audio.addEventListener('durationchange', updatePlayerUI);

      function playTrackAt(index) {
        if (index < 0 || index >= currentTracks.length) return;
        currentTrackIndex = index;
        const track = currentTracks[index];
        audio.src = track.preview;
        audio.currentTime = 0;
        audio.play().then(() => {
          status.textContent = 'Playing...';
        }).catch(err => {
          status.textContent = 'Error: ' + err.message;
        });
        updatePlayerUI();
      }

      playPauseBtn.addEventListener('click', () => {
        if (audio.src && !audio.paused) {
          audio.pause();
        } else if (audio.src) {
          audio.play();
        } else if (currentTrackIndex >= 0) {
          playTrackAt(currentTrackIndex);
        }
      });
      prevBtn.addEventListener('click', () => {
        if (currentTracks.length > 0) {
          let idx = currentTrackIndex > 0 ? currentTrackIndex - 1 : currentTracks.length - 1;
          playTrackAt(idx);
        }
      });
      nextBtn.addEventListener('click', () => {
        if (currentTracks.length > 0) {
          let idx = (currentTrackIndex + 1) % currentTracks.length;
          playTrackAt(idx);
        }
      });
      audio.addEventListener('ended', () => {
        status.textContent = 'Ended';
        nextBtn.click();
      });
      audio.addEventListener('pause', () => {
        if (!audio.ended) status.textContent = 'Paused';
        updatePlayerUI();
      });
      audio.addEventListener('play', () => {
        status.textContent = 'Playing...';
        updatePlayerUI();
      });
      audio.addEventListener('error', () => {
        status.textContent = 'Error: Could not play audio.';
      });

      // Listen for play button clicks in the chart
      document.body.addEventListener('click', (e) => {
        const playBtn = e.target.closest('.chart-action-btn.play');
        if (playBtn && playBtn.dataset.preview) {
          // Determine section: chart-item (songs/rising) or chart-card (artists/albums)
          let items, getInfo;
          if (playBtn.closest('.chart-item')) {
            items = Array.from(document.querySelectorAll('.chart-item .chart-action-btn.play'));
            getInfo = btn => {
              const item = btn.closest('.chart-item');
              return {
                preview: btn.dataset.preview,
                title: item?.querySelector('.chart-item-title')?.textContent || btn.dataset.title || '',
                artist: item?.querySelector('.chart-item-artist')?.textContent || btn.dataset.artist || ''
              };
            };
          } else if (playBtn.closest('.chart-card')) {
            items = Array.from(document.querySelectorAll('.chart-card .chart-action-btn.play'));
            getInfo = btn => {
              const card = btn.closest('.chart-card');
              return {
                preview: btn.dataset.preview,
                title: btn.dataset.title || card?.querySelector('.chart-card-title')?.textContent || '',
                artist: btn.dataset.artist || card?.querySelector('.chart-card-subtitle')?.textContent || ''
              };
            };
          } else {
            items = [playBtn];
            getInfo = btn => ({ preview: btn.dataset.preview, title: btn.dataset.title || '', artist: btn.dataset.artist || '' });
          }
          currentTracks = items.map(getInfo);
          const idx = items.indexOf(playBtn);
          playTrackAt(idx);
        }
      });
      // For initial test button
      document.getElementById('playTestBtn')?.addEventListener('click', () => {
        currentTracks = [{ preview: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3", title: "Test Audio", artist: "" }];
        playTrackAt(0);
      });
      updatePlayerUI();
    </script>
    <!-- End Simple Audio Player -->
    <script src="js/main.js"></script>
    <script src="js/trendingcharts.js"></script>
</body>
</html>